/**
 * X Integration Utilities
 * Handles image downloads, clipboard operations, and X Web Intent integration
 */

export interface XShareResult {
  success: boolean;
  message: string;
  imageCopied: boolean;
  imageDownloaded: boolean;
  textCopied: boolean;
  xOpened: boolean;
}

/**
 * Downloads an image from a URL to the user's device
 */
export const downloadImage = async (imageUrl: string, filename?: string): Promise<boolean> => {
  try {
    console.log('🔽 Starting image download:', { imageUrl, filename });
    
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
    }
    
    const blob = await response.blob();
    console.log('📦 Image blob created:', { size: blob.size, type: blob.type });
    
    // Create download link
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || `tears-of-the-left-${Date.now()}.png`;
    
    // Trigger download
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Cleanup
    window.URL.revokeObjectURL(url);
    
    console.log('✅ Image download completed successfully');
    return true;
  } catch (error) {
    console.error('❌ Image download failed:', error);
    return false;
  }
};

/**
 * Copies text to clipboard
 */
export const copyTextToClipboard = async (text: string): Promise<boolean> => {
  try {
    console.log('📋 Copying text to clipboard:', { textLength: text.length });
    
    if (navigator.clipboard && window.isSecureContext) {
      // Modern Clipboard API
      await navigator.clipboard.writeText(text);
      console.log('✅ Text copied using Clipboard API');
      return true;
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      const result = document.execCommand('copy');
      document.body.removeChild(textArea);
      
      if (result) {
        console.log('✅ Text copied using execCommand fallback');
        return true;
      } else {
        throw new Error('execCommand copy failed');
      }
    }
  } catch (error) {
    console.error('❌ Text copy failed:', error);
    return false;
  }
};

/**
 * Copies an image to clipboard (modern browsers only)
 */
export const copyImageToClipboard = async (imageUrl: string): Promise<boolean> => {
  try {
    console.log('🖼️ Copying image to clipboard:', { imageUrl });
    
    // Check if Clipboard API is available
    if (!navigator.clipboard || !window.ClipboardItem) {
      console.log('⚠️ Clipboard API not available for images');
      return false;
    }
    
    // Fetch the image
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.status}`);
    }
    
    const blob = await response.blob();
    console.log('📦 Image blob for clipboard:', { size: blob.size, type: blob.type });
    
    // Create ClipboardItem
    const clipboardItem = new ClipboardItem({
      [blob.type]: blob
    });
    
    // Copy to clipboard
    await navigator.clipboard.write([clipboardItem]);
    console.log('✅ Image copied to clipboard successfully');
    return true;
  } catch (error) {
    console.error('❌ Image clipboard copy failed:', error);
    return false;
  }
};

/**
 * Detects if the user is on a mobile device
 */
export const isMobileDevice = (): boolean => {
  if (typeof window === 'undefined') return false;

  // Check user agent for mobile indicators
  const userAgent = navigator.userAgent.toLowerCase();
  const mobileKeywords = ['android', 'iphone', 'ipad', 'ipod', 'blackberry', 'windows phone', 'mobile'];

  const isMobileUA = mobileKeywords.some(keyword => userAgent.includes(keyword));

  // Also check for touch capability and screen size
  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  const isSmallScreen = window.innerWidth <= 768;

  return isMobileUA || (isTouchDevice && isSmallScreen);
};

/**
 * Detects if the user is on iOS
 */
export const isIOS = (): boolean => {
  if (typeof window === 'undefined') return false;

  const userAgent = navigator.userAgent.toLowerCase();
  return /iphone|ipad|ipod/.test(userAgent);
};

/**
 * Detects if the user is on Android
 */
export const isAndroid = (): boolean => {
  if (typeof window === 'undefined') return false;

  const userAgent = navigator.userAgent.toLowerCase();
  return /android/.test(userAgent);
};

/**
 * Opens X with mobile app deep linking or web fallback
 */
export const openXIntent = (text: string): boolean => {
  try {
    console.log('❌ Opening X Intent:', {
      textLength: text.length,
      isMobile: isMobileDevice(),
      isIOS: isIOS(),
      isAndroid: isAndroid()
    });

    const encodedText = encodeURIComponent(text);

    if (isMobileDevice()) {
      console.log('📱 Mobile device detected, attempting app deep link...');

      if (isIOS()) {
        // iOS X app deep link (still uses twitter:// scheme)
        const iosDeepLink = `twitter://post?message=${encodedText}`;
        const webFallback = `https://x.com/intent/tweet?text=${encodedText}`;

        console.log('🍎 iOS deep link:', iosDeepLink);

        // Try to open the X app
        const startTime = Date.now();
        window.location.href = iosDeepLink;

        // Fallback to web if app doesn't open within 2 seconds
        setTimeout(() => {
          // If we're still on the same page after 2 seconds, the app probably didn't open
          if (Date.now() - startTime > 1500) {
            console.log('⚠️ X app not available, falling back to web...');
            window.open(webFallback, '_blank', 'noopener,noreferrer');
          }
        }, 2000);

      } else if (isAndroid()) {
        // Android X app deep link (still uses twitter:// scheme)
        const androidDeepLink = `twitter://post?message=${encodedText}`;
        const webFallback = `https://x.com/intent/tweet?text=${encodedText}`;

        console.log('🤖 Android deep link:', androidDeepLink);

        // Try to open the X app
        try {
          window.location.href = androidDeepLink;

          // Fallback to web if app doesn't open
          setTimeout(() => {
            console.log('⚠️ X app not available, falling back to web...');
            window.open(webFallback, '_blank', 'noopener,noreferrer');
          }, 2000);

        } catch {
          console.log('⚠️ Deep link failed, opening web fallback...');
          window.open(webFallback, '_blank', 'noopener,noreferrer');
        }

      } else {
        // Other mobile devices - use web intent
        console.log('📱 Other mobile device, using web intent...');
        const webUrl = `https://x.com/intent/tweet?text=${encodedText}`;
        window.open(webUrl, '_blank', 'noopener,noreferrer');
      }

    } else {
      // Desktop - use web intent
      console.log('💻 Desktop device, using web intent...');
      const xUrl = `https://x.com/intent/tweet?text=${encodedText}`;
      window.open(xUrl, '_blank', 'noopener,noreferrer');
    }

    console.log('✅ X intent opened successfully');
    return true;
  } catch (error) {
    console.error('❌ X intent failed:', error);
    return false;
  }
};

/**
 * Main function to share image and text to X
 * Attempts multiple strategies with fallbacks
 */
export const shareToX = async (
  imageUrl: string,
  text: string,
  filename?: string
): Promise<XShareResult> => {
  console.log('🚀 Starting X share process:', { imageUrl, text, filename });
  
  const result: XShareResult = {
    success: false,
    message: '',
    imageCopied: false,
    imageDownloaded: false,
    textCopied: false,
    xOpened: false
  };
  
  try {
    // Step 1: Try to copy image to clipboard
    console.log('📋 Step 1: Attempting image clipboard copy...');
    result.imageCopied = await copyImageToClipboard(imageUrl);
    
    // Step 2: Download image as backup
    console.log('💾 Step 2: Downloading image as backup...');
    result.imageDownloaded = await downloadImage(imageUrl, filename);
    
    // Step 3: Copy text to clipboard (if image copy failed)
    if (!result.imageCopied) {
      console.log('📝 Step 3: Copying text to clipboard as fallback...');
      result.textCopied = await copyTextToClipboard(text);
    }
    
    // Step 4: Open X (with mobile app support)
    console.log('❌ Step 4: Opening X with mobile app support...');
    result.xOpened = await openXWithFallback(text);
    
    // Determine success and message based on platform
    const appInfo = getMobileAppInfo();
    const platformText = appInfo.isMobile ?
      (appInfo.hasXApp ? 'X app' : 'mobile X') :
      'X';

    if (result.imageCopied && result.xOpened) {
      result.success = true;
      result.message = `Image copied to clipboard! ${platformText} opened with your text. Just paste the image in your post.`;
    } else if (result.imageDownloaded && result.xOpened) {
      result.success = true;
      if (result.textCopied) {
        result.message = `Image downloaded and text copied! ${platformText} opened. Upload the downloaded image to your post.`;
      } else {
        result.message = `Image downloaded! ${platformText} opened with your text. Upload the downloaded image to your post.`;
      }
    } else if (result.xOpened) {
      result.success = true;
      result.message = `${platformText} opened with your text. Please manually save and upload the image.`;
    } else {
      result.success = false;
      result.message = 'Unable to open X. Please copy your text and image manually.';
    }
    
    console.log('🎯 X share result:', result);
    return result;
    
  } catch (error) {
    console.error('💥 X share process failed:', error);
    result.success = false;
    result.message = 'An error occurred while sharing to X. Please try again.';
    return result;
  }
};

/**
 * Validates post text length (X's limit is 280 characters)
 */
export const validatePostText = (text: string): { isValid: boolean; remainingChars: number } => {
  const maxLength = 280;
  const remainingChars = maxLength - text.length;
  
  return {
    isValid: remainingChars >= 0,
    remainingChars
  };
};

/**
 * Generates suggested hashtags for the image
 */
export const getSuggestedHashtags = (): string[] => {
  return [
    '#TearsOfTheLeft',
    '#AIArt',
    '#ImageTransformation',
    '#DigitalArt',
    '#CreativeAI'
  ];
};

/**
 * Creates a default post text with @CheersToTears mention
 */
export const createDefaultPostText = (): string => {
  return `Add your post text here!

Made with @CheersToTears`;
};

/**
 * Enhanced mobile app detection with more specific checks
 */
export const getMobileAppInfo = () => {
  if (typeof window === 'undefined') {
    return { isMobile: false, platform: 'unknown', hasTwitterApp: false };
  }

  const userAgent = navigator.userAgent.toLowerCase();
  const isMobile = isMobileDevice();

  let platform = 'unknown';
  let hasXApp = false;

  if (isIOS()) {
    platform = 'ios';
    // iOS users are more likely to have X app installed
    hasXApp = true;
  } else if (isAndroid()) {
    platform = 'android';
    // Android users are also likely to have X app
    hasXApp = true;
  } else if (isMobile) {
    platform = 'mobile-other';
    hasXApp = false;
  } else {
    platform = 'desktop';
    hasXApp = false;
  }

  return {
    isMobile,
    platform,
    hasXApp,
    userAgent: userAgent.substring(0, 100) // Truncated for logging
  };
};

/**
 * Creates platform-specific X URLs
 */
export const createXUrls = (text: string) => {
  const encodedText = encodeURIComponent(text);

  return {
    // iOS X app deep link (still uses twitter:// scheme)
    iosApp: `twitter://post?message=${encodedText}`,

    // Android X app deep link (still uses twitter:// scheme)
    androidApp: `twitter://post?message=${encodedText}`,

    // Alternative Android intent (more reliable on some devices)
    androidIntent: `intent://post?message=${encodedText}#Intent;scheme=twitter;package=com.twitter.android;S.browser_fallback_url=https%3A%2F%2Fx.com%2Fintent%2Ftweet%3Ftext%3D${encodedText};end`,

    // Web fallback
    web: `https://x.com/intent/tweet?text=${encodedText}`,

    // Mobile web (optimized for mobile browsers)
    mobileWeb: `https://x.com/compose/tweet?text=${encodedText}`
  };
};

/**
 * Advanced X app opening with better fallback handling
 */
export const openXWithFallback = (text: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const appInfo = getMobileAppInfo();
    const urls = createXUrls(text);

    console.log('❌ Advanced X opening:', { appInfo, urls });

    if (!appInfo.isMobile) {
      // Desktop - just open web version
      console.log('💻 Desktop: Opening web X...');
      window.open(urls.web, '_blank', 'noopener,noreferrer');
      resolve(true);
      return;
    }

    // Mobile device - try app first, then fallback
    console.log('📱 Mobile: Attempting app deep link...');

    let appOpened = false;

    // Function to handle fallback
    const handleFallback = () => {
      if (!appOpened) {
        console.log('⚠️ App not available, using web fallback...');

        // Use mobile-optimized web version for better UX
        const fallbackUrl = appInfo.platform === 'ios' ? urls.mobileWeb : urls.web;
        window.open(fallbackUrl, '_blank', 'noopener,noreferrer');
      }
      resolve(true);
    };

    // Set up fallback timer
    const fallbackTimer = setTimeout(handleFallback, 2500);

    // Listen for page visibility change (indicates app opened)
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Page became hidden, likely because app opened
        appOpened = true;
        clearTimeout(fallbackTimer);
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        console.log('✅ X app opened successfully');
        resolve(true);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Try to open the app
    try {
      if (appInfo.platform === 'ios') {
        window.location.href = urls.iosApp;
      } else if (appInfo.platform === 'android') {
        // Try the intent URL first (more reliable)
        window.location.href = urls.androidIntent;
      } else {
        // Other mobile - go straight to web
        clearTimeout(fallbackTimer);
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        handleFallback();
      }
    } catch (error) {
      console.error('❌ Deep link failed:', error);
      clearTimeout(fallbackTimer);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      handleFallback();
    }
  });
};

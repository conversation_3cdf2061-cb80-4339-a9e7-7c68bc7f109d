import Dexie, { Table } from 'dexie';

// Interface for stored image data
export interface StoredImage {
  id: string;
  originalImageUrl: string;
  processedImageUrl: string;
  originalFileName: string;
  processedAt: Date;
  lastAccessed: Date;
  prompt: string;
  model: string;
  fileSize: number;
  thumbnail?: string; // Base64 thumbnail for quick preview
}

// Dexie database class
class ImageDatabase extends Dexie {
  images!: Table<StoredImage>;

  constructor() {
    super('TearsOfTheLeftImages');
    
    this.version(1).stores({
      images: 'id, processedAt, lastAccessed, originalFileName'
    });
  }
}

// Create database instance
const db = new ImageDatabase();

// Maximum number of images to store
const MAX_IMAGES = 5;

/**
 * Generates a thumbnail from base64 image data
 */
async function generateThumbnail(base64Image: string, maxSize: number = 150): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        reject(new Error('Could not get canvas context'));
        return;
      }

      // Calculate thumbnail dimensions
      const { width, height } = img;
      const aspectRatio = width / height;
      
      let thumbnailWidth = maxSize;
      let thumbnailHeight = maxSize;
      
      if (aspectRatio > 1) {
        thumbnailHeight = maxSize / aspectRatio;
      } else {
        thumbnailWidth = maxSize * aspectRatio;
      }

      canvas.width = thumbnailWidth;
      canvas.height = thumbnailHeight;
      
      // Draw and compress
      ctx.drawImage(img, 0, 0, thumbnailWidth, thumbnailHeight);
      const thumbnail = canvas.toDataURL('image/jpeg', 0.7);
      resolve(thumbnail);
    };
    
    img.onerror = () => reject(new Error('Failed to load image for thumbnail'));
    img.src = base64Image;
  });
}

/**
 * Calculates file size from base64 string
 */
function calculateBase64Size(base64String: string): number {
  // Remove data URL prefix if present
  const base64Data = base64String.replace(/^data:image\/[a-z]+;base64,/, '');
  // Base64 encoding increases size by ~33%, so actual size is roughly 3/4 of base64 length
  return Math.round((base64Data.length * 3) / 4);
}

/**
 * Stores a processed image with automatic cleanup of old images
 */
export async function storeProcessedImage(
  originalImageUrl: string,
  processedImageUrl: string,
  originalFileName: string,
  prompt: string,
  model: string = 'OpenAI DALL-E'
): Promise<void> {
  try {
    console.log('🗄️ Storing processed image locally...');
    
    const now = new Date();
    const id = `img_${now.getTime()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Generate thumbnail for quick preview
    const thumbnail = await generateThumbnail(processedImageUrl);
    
    // Calculate file size
    const fileSize = calculateBase64Size(processedImageUrl);
    
    const imageData: StoredImage = {
      id,
      originalImageUrl,
      processedImageUrl,
      originalFileName,
      processedAt: now,
      lastAccessed: now,
      prompt,
      model,
      fileSize,
      thumbnail
    };

    // Store the new image
    await db.images.add(imageData);
    console.log(`✅ Image stored with ID: ${id}`);

    // Clean up old images (keep only the latest 5)
    await cleanupOldImages();
    
  } catch (error) {
    console.error('❌ Failed to store image:', error);
    throw new Error('Failed to store image locally');
  }
}

/**
 * Retrieves all stored images, sorted by most recent first
 */
export async function getStoredImages(): Promise<StoredImage[]> {
  try {
    const images = await db.images
      .orderBy('processedAt')
      .reverse()
      .limit(MAX_IMAGES)
      .toArray();
    
    console.log(`📁 Retrieved ${images.length} stored images`);
    return images;
  } catch (error) {
    console.error('❌ Failed to retrieve stored images:', error);
    return [];
  }
}

/**
 * Gets a specific image by ID and updates its last accessed time
 */
export async function getImageById(id: string): Promise<StoredImage | null> {
  try {
    const image = await db.images.get(id);
    
    if (image) {
      // Update last accessed time
      await db.images.update(id, { lastAccessed: new Date() });
      console.log(`👁️ Accessed image: ${id}`);
    }
    
    return image || null;
  } catch (error) {
    console.error('❌ Failed to get image by ID:', error);
    return null;
  }
}

/**
 * Removes old images to maintain the maximum limit
 */
async function cleanupOldImages(): Promise<void> {
  try {
    const allImages = await db.images.orderBy('processedAt').reverse().toArray();
    
    if (allImages.length > MAX_IMAGES) {
      const imagesToDelete = allImages.slice(MAX_IMAGES);
      const idsToDelete = imagesToDelete.map(img => img.id);
      
      await db.images.bulkDelete(idsToDelete);
      console.log(`🧹 Cleaned up ${idsToDelete.length} old images`);
    }
  } catch (error) {
    console.error('❌ Failed to cleanup old images:', error);
  }
}

/**
 * Clears all stored images
 */
export async function clearAllImages(): Promise<void> {
  try {
    await db.images.clear();
    console.log('🗑️ All stored images cleared');
  } catch (error) {
    console.error('❌ Failed to clear images:', error);
    throw new Error('Failed to clear stored images');
  }
}

/**
 * Gets storage usage statistics
 */
export async function getStorageStats(): Promise<{
  count: number;
  totalSize: number;
  formattedSize: string;
}> {
  try {
    const images = await db.images.toArray();
    const totalSize = images.reduce((sum, img) => sum + img.fileSize, 0);
    
    // Format size in human-readable format
    const formatSize = (bytes: number): string => {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    return {
      count: images.length,
      totalSize,
      formattedSize: formatSize(totalSize)
    };
  } catch (error) {
    console.error('❌ Failed to get storage stats:', error);
    return { count: 0, totalSize: 0, formattedSize: '0 B' };
  }
}

/**
 * Checks if local storage is available and working
 */
export async function isStorageAvailable(): Promise<boolean> {
  try {
    // Test if we can perform basic operations
    await db.images.limit(1).toArray();
    return true;
  } catch (error) {
    console.error('❌ Local storage not available:', error);
    return false;
  }
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Enhanced brand colors for better readability */
    --background: 185 100% 14%;         /* #004643 - Deep teal background */
    --foreground: 0 0% 100%;            /* #fffffe - Pure white text */
    --card: 185 100% 14%;               /* Same as background to avoid box effect */
    --card-foreground: 0 0% 100%;       /* #fffffe - Pure white text */
    --popover: 185 50% 20%;             /* Lighter teal for popovers */
    --popover-foreground: 0 0% 100%;    /* #fffffe - Pure white text */
    --primary: 44 95% 75%;              /* #f9bc60 - Brighter button color */
    --primary-foreground: 180 96% 6%;   /* #001e1d - Dark text on buttons */
    --secondary: 159 35% 80%;           /* #abd1c6 - Brighter secondary color */
    --secondary-foreground: 180 96% 6%; /* #001e1d - Dark text */
    --muted: 159 35% 80%;               /* #abd1c6 - Brighter muted color */
    --muted-foreground: 180 96% 6%;     /* #001e1d - Dark text */
    --accent: 44 95% 75%;               /* #f9bc60 - Brighter accent */
    --accent-foreground: 180 96% 6%;    /* #001e1d - Dark text */
    --destructive: 0 80% 65%;           /* #e16162 - Brighter red */
    --destructive-foreground: 0 0% 100%; /* #fffffe - White text */
    --border: 159 35% 80%;              /* #abd1c6 - Lighter border */
    --input: 185 50% 20%;               /* Lighter teal for inputs */
    --ring: 44 95% 75%;                 /* #f9bc60 - Bright ring */
    --chart-1: 44 95% 75%;              /* #f9bc60 - Bright chart color */
    --chart-2: 159 35% 80%;             /* #abd1c6 - Bright chart color */
    --chart-3: 0 80% 65%;               /* #e16162 - Bright chart color */
    --chart-4: 90 10% 95%;              /* #e8e4e6 - Bright chart color */
    --chart-5: 180 96% 6%;              /* #001e1d - Dark chart color */
    --radius: 0.5rem;
  }
  .dark {
    /* Dark mode with enhanced contrast */
    --background: 180 96% 6%;           /* #001e1d - Very dark background */
    --foreground: 0 0% 100%;            /* #fffffe - Pure white text */
    --card: 180 96% 6%;                 /* Same as background to avoid box effect */
    --card-foreground: 0 0% 100%;       /* #fffffe - Pure white text */
    --popover: 185 80% 18%;             /* #004643 - Teal popovers */
    --popover-foreground: 0 0% 100%;    /* #fffffe - Pure white text */
    --primary: 44 95% 75%;              /* #f9bc60 - Bright button color */
    --primary-foreground: 180 96% 6%;   /* #001e1d - Dark text on buttons */
    --secondary: 159 35% 80%;           /* #abd1c6 - Bright secondary */
    --secondary-foreground: 180 96% 6%; /* #001e1d - Dark text */
    --muted: 159 35% 80%;               /* #abd1c6 - Bright muted */
    --muted-foreground: 180 96% 6%;     /* #001e1d - Dark text */
    --accent: 44 95% 75%;               /* #f9bc60 - Bright accent */
    --accent-foreground: 180 96% 6%;    /* #001e1d - Dark text */
    --destructive: 0 80% 65%;           /* #e16162 - Bright red */
    --destructive-foreground: 0 0% 100%; /* #fffffe - White text */
    --border: 159 35% 80%;              /* #abd1c6 - Bright border */
    --input: 185 80% 18%;               /* #004643 - Teal inputs */
    --ring: 44 95% 75%;                 /* #f9bc60 - Bright ring */
    --chart-1: 44 95% 75%;              /* #f9bc60 - Bright chart color */
    --chart-2: 159 35% 80%;             /* #abd1c6 - Bright chart color */
    --chart-3: 0 80% 65%;               /* #e16162 - Bright chart color */
    --chart-4: 90 10% 95%;              /* #e8e4e6 - Bright chart color */
    --chart-5: 180 96% 6%;              /* #001e1d - Dark chart color */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  html, body {
    @apply bg-background text-foreground;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
  }
  
  #__next {
    width: 100%;
    min-height: 100vh;
  }
}

@layer utilities {
  .fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .slide-in {
    animation: slideIn 0.3s ease-out;
  }
  
  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .animate-pulse-slow {
    animation: pulse-slow 3s ease-in-out infinite;
  }
  
  @keyframes pulse-slow {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
  }
  
  .animate-spin-slow {
    animation: spin-slow 8s linear infinite;
  }
  
  @keyframes spin-slow {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
  
  .animate-star-clockwise {
    animation: star-clockwise 4s ease-in-out infinite;
  }
  
  @keyframes star-clockwise {
    0% {
      opacity: 0;
      stroke-dasharray: 0 1000;
      transform: scale(0.3) rotate(-90deg);
    }
    25% {
      opacity: 1;
      stroke-dasharray: 500 1000;
      transform: scale(1.1) rotate(0deg);
    }
    50% {
      opacity: 1;
      stroke-dasharray: 1000 1000;
      transform: scale(1) rotate(90deg);
    }
    75% {
      opacity: 0.8;
      stroke-dasharray: 1000 1000;
      transform: scale(0.9) rotate(180deg);
    }
    100% {
      opacity: 0;
      stroke-dasharray: 0 1000;
      transform: scale(0.3) rotate(270deg);
    }
  }
}

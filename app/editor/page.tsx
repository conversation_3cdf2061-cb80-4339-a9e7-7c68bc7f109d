'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Upload, Sparkles, LogOut, Download } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import Image from 'next/image';
import { XPostSection } from '@/components/x-post-section';
import { RecentImages } from '@/components/recent-images';
import { useStoredImages } from '@/hooks/useImageStorage';
import { StoredImage } from '@/lib/imageStorage';
import { Toaster } from 'sonner';
import { Progress } from '@/components/ui/progress';

// Hardcoded prompt for "Tears of the left" effect
const HARDCODED_PROMPT = "Retro cartoon illustration. Thick black outlines, smooth flat shading, limited warm vintage palette (muted oranges, ochres, teal accents). 1950s newspaper comic style, rounded shapes, subtle paper-grain texture. Clean vector aesthetic, high-resolution, simple background with soft abstract swirls in tan. The character must have a crying face with a tear running down the left cheek.";

interface ProcessingResult {
  editedImageUrl: string;
  originalPrompt: string;
  processedAt: string;
  model: string;
}

export default function EditorPage() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [processingStep, setProcessingStep] = useState('');
  const [showComplete, setShowComplete] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<ProcessingResult | null>(null);
  const [uploadedImageData, setUploadedImageData] = useState<string | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const router = useRouter();
  const supabase = createClient();
  const { storeImage } = useStoredImages();

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push('/');
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      if (file.type.startsWith('image/')) {
        handleFileSelect(file);
      } else {
        setError('Please drop an image file');
      }
    }
  };

  const uploadWithProgress = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const formData = new FormData();
      formData.append('file', file);

      const xhr = new XMLHttpRequest();

      // Track upload progress
      xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
          const progress = Math.round((e.loaded / e.total) * 100);
          setUploadProgress(progress);
        }
      });

      xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
          try {
            const data = JSON.parse(xhr.responseText);
            resolve(data.image);
          } catch (err) {
            reject(new Error('Invalid response format'));
          }
        } else {
          try {
            const errorData = JSON.parse(xhr.responseText);
            reject(new Error(errorData.error || 'Upload failed'));
          } catch {
            reject(new Error(`Upload failed with status ${xhr.status}`));
          }
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Upload failed'));
      });

      xhr.open('POST', '/api/upload');
      xhr.send(formData);
    });
  };

  const handleFileSelect = async (file: File | null) => {
    setSelectedFile(file);
    setError(null);
    setResult(null);
    setUploadedImageData(null);
    setUploadProgress(0);

    if (file) {
      setIsUploading(true);
      try {
        const imageData = await uploadWithProgress(file);
        setUploadedImageData(imageData);
        setUploadProgress(100);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Upload failed');
        setSelectedFile(null); // Reset file on error
      } finally {
        setIsUploading(false);
      }
    }
  };

  const simulateProcessingProgress = () => {
    const steps = [
      { label: 'Analyzing image structure...', progress: 20, delay: 1000 },
      { label: 'Understanding emotional context...', progress: 40, delay: 2000 },
      { label: 'Applying transformation effects...', progress: 70, delay: 3000 },
      { label: 'Adding tears and emotional details...', progress: 90, delay: 1500 },
      { label: 'Finalizing your masterpiece...', progress: 100, delay: 500 }
    ];

    let currentStep = 0;
    
    const processStep = () => {
      if (currentStep < steps.length) {
        const step = steps[currentStep];
        setProcessingStep(step.label);
        setProcessingProgress(step.progress);
        currentStep++;
        
        setTimeout(processStep, step.delay);
      }
    };
    
    // Start immediately
    processStep();
  };

  const handleProcess = async () => {
    if (!uploadedImageData) {
      setError('Please upload an image first');
      return;
    }

    setIsProcessing(true);
    setError(null);
    setResult(null);
    setProcessingProgress(0);
    setProcessingStep('Preparing your image...');

    // Start the progress simulation
    simulateProcessingProgress();

    try {
      const response = await fetch('/api/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageData: uploadedImageData,
          prompt: HARDCODED_PROMPT,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Processing failed');
      }

      const data = await response.json();
      
      // Ensure we show 100% completion
      setProcessingProgress(100);
      setProcessingStep('Transformation complete!');
      
      setResult(data);
      setShowComplete(true);
      setTimeout(() => setShowComplete(false), 2000);

      // Store the processed image locally
      if (selectedFile && data.editedImageUrl) {
        try {
          console.log('🗄️ Storing processed image locally...');
          await storeImage(
            uploadedImageData || '',
            data.editedImageUrl,
            selectedFile.name,
            HARDCODED_PROMPT,
            data.model || 'OpenAI DALL-E'
          );
          console.log('✅ Image stored successfully');
        } catch (storageError) {
          console.error('❌ Failed to store image locally:', storageError);
          // Don't show error to user as the main processing succeeded
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Processing failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const canProcess = uploadedImageData && !isProcessing && !isUploading;

  // Handle restoring an image from recent images
  const handleImageRestore = (storedImage: StoredImage) => {
    console.log('🔄 Restoring image from storage:', storedImage.originalFileName);

    // Create a mock file object for the UI
    const mockFile = new File([''], storedImage.originalFileName, { type: 'image/png' });
    setSelectedFile(mockFile);
    setUploadedImageData(storedImage.originalImageUrl);

    // Set the result to show the processed image
    setResult({
      editedImageUrl: storedImage.processedImageUrl,
      originalPrompt: storedImage.prompt,
      processedAt: storedImage.processedAt.toISOString(),
      model: storedImage.model
    });

    // Clear any errors
    setError(null);
    setShowComplete(false);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="sticky top-0 z-50 w-full border-b border-accent/20 bg-background/80 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-6 py-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 relative">
              <Image
                src="/logo-transparent.svg"
                alt="Tears of the Left Logo"
                width={40}
                height={40}
                className="w-full h-full object-contain"
              />
            </div>
            <h1 className="text-2xl font-bold text-foreground">
              Tears of the Left
            </h1>
          </div>
          <Button 
            onClick={handleSignOut}
            variant="outline" 
            size="sm"
            className="flex items-center gap-2 border-accent/50 hover:bg-accent hover:text-accent-foreground hover:border-accent transition-all duration-200"
          >
            <LogOut className="h-4 w-4" />
            Sign Out
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="w-full px-6 py-8">
        <div className="max-w-4xl mx-auto space-y-8 fade-in">
          {/* Upload Section */}
          <Card 
            className={`border-2 border-dashed transition-all duration-300 hover:shadow-lg ${
              isDragOver 
                ? 'border-accent bg-accent/10 shadow-lg scale-[1.02]' 
                : 'border-secondary/30 bg-secondary/5'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <CardContent className="p-8">
              {!selectedFile ? (
                <div className="text-center space-y-6">
                  <div className="mx-auto w-20 h-20 bg-accent/20 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110">
                    <Upload className={`h-10 w-10 transition-colors ${isDragOver ? 'text-accent' : 'text-muted-foreground'}`} />
                  </div>
                  <div className="space-y-3">
                    <h3 className="text-xl font-bold text-foreground">
                      Upload your image
                    </h3>
                    <p className="text-foreground/80 text-base">
                      {isDragOver 
                        ? '✨ Drop your image here' 
                        : 'Transform with the "Tears of the Left" effect'
                      }
                    </p>
                  </div>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleFileSelect(e.target.files?.[0] || null)}
                    className="hidden"
                    id="file-upload"
                    disabled={isProcessing || isUploading}
                  />
                  <Button 
                    asChild 
                    className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 text-base font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
                    disabled={isProcessing || isUploading}
                  >
                    <label htmlFor="file-upload" className="cursor-pointer">
                      Choose Image
                    </label>
                  </Button>
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="flex items-center justify-between p-4 bg-accent/10 rounded-lg border border-accent/20">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-accent/20 rounded-full flex items-center justify-center">
                        <Upload className="h-6 w-6 text-accent" />
                      </div>
                      <div>
                        <p className="font-semibold text-foreground text-base">
                          {selectedFile.name}
                        </p>
                        <div className="text-sm text-foreground/70">
                          <div className="flex items-center gap-2 mb-1">

                            {
                              isUploading ? (
                                <span className="inline-flex items-center gap-1">
                                  <span className="w-3 h-3 border border-accent/30 border-t-accent rounded-full animate-spin block"></span>
                                  Uploading {uploadProgress}%...
                                </span>
                              ) : uploadedImageData ? (
                                <span className="text-green-600 font-medium">✓ Ready to transform</span>
                              ) : (
                                "Processing..."
                              )
                            }
                          </div>
                          {isUploading && (
                            <Progress 
                              value={uploadProgress} 
                              className="h-2 mt-1"
                            />
                          )}
                        </div>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleFileSelect(null)}
                      disabled={isProcessing || isUploading}
                      className="border-accent/30 hover:bg-accent/10 text-foreground"
                    >
                      Change
                    </Button>
                  </div>
                  
                  <Button
                    onClick={handleProcess}
                    disabled={!canProcess}
                    className="w-full h-14 text-lg bg-primary hover:bg-primary/90 text-primary-foreground font-bold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
                    size="lg"
                  >
                    {isProcessing ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-foreground mr-3" />
                        Creating tears...
                      </>
                    ) : isUploading ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-foreground mr-3" />
                        Uploading...
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-6 w-6 mr-3" />
                        Transform Image
                      </>
                    )}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Error Display */}
          {error && (
            <Card className="border-destructive/50 bg-destructive/10 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center gap-3 text-center justify-center">
                  <div className="w-8 h-8 bg-destructive/20 rounded-full flex items-center justify-center">
                    <span className="text-destructive font-bold">!</span>
                  </div>
                  <p className="text-destructive font-medium text-base">{error}</p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Loading Animation */}
          {isProcessing && (
            <Card className="border-accent/30 bg-secondary/5 shadow-xl">
              <CardContent className="p-8">
                <div className="space-y-8">
                  <div className="text-center">
                    <h3 className="text-2xl font-bold text-foreground mb-3">
                      Creating your masterpiece...
                    </h3>
                    <p className="text-base text-foreground/80">
                      The AI is painting tears of emotion onto your image
                    </p>
                  </div>
                  
                  {/* Processing Progress */}
                  <div className="relative rounded-xl overflow-hidden bg-accent/5 border border-accent/20 p-8">
                    <div className="space-y-6 text-center">
                      {/* Progress Circle */}
                      <div className="relative">
                        <div className="w-20 h-20 mx-auto relative">
                          <div className="absolute inset-0 rounded-full bg-accent/30 animate-ping opacity-75"></div>
                          <div className="absolute inset-2 rounded-full bg-accent/50 animate-pulse"></div>
                          <div className="absolute inset-4 rounded-full bg-accent flex items-center justify-center text-white font-bold">
                            {processingProgress}%
                          </div>
                        </div>
                      </div>
                      
                      {/* Current Step */}
                      <div className="space-y-4">
                        <div className="text-lg font-semibold text-foreground">
                          {processingStep}
                        </div>
                        
                        {/* Progress Bar */}
                        <div className="max-w-md mx-auto">
                          <Progress 
                            value={processingProgress} 
                            className="h-3"
                          />
                        </div>
                        
                        {/* Loading dots */}
                        <div className="flex justify-center space-x-2">
                          <div className="w-2 h-2 bg-accent rounded-full animate-bounce" style={{animationDelay: '0s'}}></div>
                          <div className="w-2 h-2 bg-accent rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                          <div className="w-2 h-2 bg-accent rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Completion Animation */}
          {showComplete && (
            <Card className="border-accent/30 bg-secondary/5 shadow-xl">
              <CardContent className="p-8">
                <div className="space-y-8">
                  <div className="text-center">
                    <h3 className="text-2xl font-bold text-foreground mb-3">
                      ✨ Transform Complete!
                    </h3>
                    <p className="text-base text-foreground/80">
                      Your image has been successfully transformed
                    </p>
                  </div>
                  
                  {/* Success Animation */}
                  <div className="relative rounded-xl overflow-hidden bg-accent/5 h-96 flex items-center justify-center border border-accent/20">
                    <div className="space-y-6 text-center">
                      {/* Success Circle with Checkmark */}
                      <div className="relative">
                        <div className="w-20 h-20 mx-auto relative">
                          <div className="absolute inset-0 rounded-full bg-green-500/30 animate-ping opacity-75"></div>
                          <div className="absolute inset-2 rounded-full bg-green-500/50 animate-pulse"></div>
                          <div className="absolute inset-4 rounded-full bg-green-500 flex items-center justify-center">
                            <svg className="w-6 h-6 text-white animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </div>
                        </div>
                      </div>
                      
                      {/* Success message */}
                      <div className="space-y-4">
                        <div className="text-lg font-semibold text-foreground">
                          Success! 🎉
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Result Display */}
          {result && !isProcessing && (
            <div className="space-y-8">
              {/* Header */}
              <div className="text-center">
                <h3 className="text-2xl font-bold text-foreground mb-3">
                  ✨ Your transformed image
                </h3>
                <p className="text-base text-foreground/80">
                  The &ldquo;Tears of the Left&rdquo; effect has been applied
                </p>
              </div>

              {/* Main Content Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:items-start">
                {/* Image Section */}
                <Card className="border-accent/30 bg-secondary/5 shadow-xl h-full">
                  <CardContent className="p-8 h-full flex flex-col">
                    <div className="space-y-6 flex-1 flex flex-col">
                      <div className="relative rounded-xl overflow-hidden bg-accent/5 border border-accent/20 p-4 flex-1 flex items-center justify-center">
                        <Image
                          src={result.editedImageUrl}
                          alt="Transformed image"
                          width={1024}
                          height={1024}
                          className="w-full h-auto max-h-96 object-contain mx-auto rounded-lg shadow-lg"
                          unoptimized={true}
                        />
                      </div>

                      <Button
                        onClick={() => {
                          const link = document.createElement('a');
                          link.href = result.editedImageUrl;
                          link.download = `tears-of-the-left-${Date.now()}.png`;
                          document.body.appendChild(link);
                          link.click();
                          document.body.removeChild(link);
                        }}
                        className="w-full h-14 bg-primary hover:bg-primary/90 text-primary-foreground font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02]"
                      >
                        <Download className="h-5 w-5 mr-3" />
                        Download Image
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* X Section */}
                <XPostSection
                  imageUrl={result.editedImageUrl}
                  className="h-full"
                />
              </div>
            </div>
          )}

          {/* Recent Images Section - Moved to bottom */}
          <RecentImages
            onImageSelect={handleImageRestore}
            className="fade-in"
          />
        </div>
      </div>
      <Toaster position="top-right" richColors />
    </div>
  );
}
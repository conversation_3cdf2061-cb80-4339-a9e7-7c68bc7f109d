"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Twitter, Send, Copy } from "lucide-react";
import { toast } from "sonner";
import {
  shareToTwitter,
  createDefaultTweetText
} from "@/lib/twitter-utils";

interface TwitterPostSectionProps {
  imageUrl: string;
  className?: string;
}

export function TwitterPostSection({ imageUrl, className }: TwitterPostSectionProps) {
  const [tweetText, setTweetText] = useState("");
  const [isSharing, setIsSharing] = useState(false);

  // Initialize with default tweet text
  useEffect(() => {
    setTweetText(createDefaultTweetText());
  }, []);

  const handleShare = async () => {
    console.log('🐦 TwitterPostSection: Starting share process');

    if (!tweetText.trim()) {
      toast.error("Please enter some text for your tweet.");
      return;
    }

    setIsSharing(true);
    
    try {
      const result = await shareToTwitter(
        imageUrl,
        tweetText,
        `tears-of-the-left-${Date.now()}.png`
      );
      
      if (result.success) {
        toast.success(result.message, {
          duration: 5000,
          action: result.imageCopied ? {
            label: "Got it!",
            onClick: () => toast.dismiss()
          } : undefined
        });
      } else {
        toast.error(result.message, {
          duration: 7000,
          action: {
            label: "Retry",
            onClick: () => handleShare()
          }
        });
      }
    } catch (error) {
      console.error('❌ TwitterPostSection: Share failed:', error);
      toast.error("Failed to share to Twitter. Please try again.");
    } finally {
      setIsSharing(false);
    }
  };



  const handleCopyText = async () => {
    try {
      await navigator.clipboard.writeText(tweetText);
      toast.success("Tweet text copied to clipboard!");
    } catch (error) {
      console.error('Failed to copy text:', error);
      toast.error("Failed to copy text to clipboard.");
    }
  };

  return (
    <Card className={`border-accent/30 bg-secondary/5 shadow-xl ${className}`}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Twitter className="h-5 w-5 text-accent" />
          Share to Twitter
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Tweet Text Input */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label htmlFor="tweet-text" className="text-sm font-medium text-foreground">
              Tweet Text
            </label>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCopyText}
              className="h-6 px-2 text-xs"
            >
              <Copy className="h-3 w-3 mr-1" />
              Copy
            </Button>
          </div>
          
          <Textarea
            id="tweet-text"
            value={tweetText}
            onChange={(e) => setTweetText(e.target.value)}
            placeholder="What's happening?"
            className="min-h-[100px] resize-none"
            maxLength={300} // Allow a bit over 280 for better UX
          />
        </div>



        {/* Instructions */}
        <div className="text-xs text-white p-3 bg-accent/5 rounded-lg border border-accent/20">
          <p className="font-medium mb-1 text-white">How it works:</p>
          <ol className="list-decimal list-inside space-y-1 text-white">
            <li>Your image will be copied to clipboard (if supported)</li>
            <li>Image will be downloaded as backup</li>
            <li>Twitter will open with your text pre-filled</li>
            <li>Paste or upload the image in your tweet</li>
          </ol>
        </div>

        {/* Share Button */}
        <Button
          onClick={handleShare}
          disabled={isSharing || !tweetText.trim()}
          className="w-full bg-primary hover:bg-primary/90 text-primary-foreground font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02] disabled:transform-none disabled:hover:scale-100 h-14"
        >
          {isSharing ? (
            <>
              <Loader2 className="h-5 w-5 mr-3 animate-spin" />
              Sharing to Twitter...
            </>
          ) : (
            <>
              <Send className="h-5 w-5 mr-3" />
              Share to Twitter
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Smartphone, 
  Monitor, 
  Twitter, 
  Info,
  CheckCircle,
  XCircle
} from 'lucide-react';
import {
  getMobileAppInfo,
  createXUrls,
  openXWithFallback,
  isIOS,
  isAndroid
} from '@/lib/twitter-utils';

/**
 * Test component for mobile Twitter deep linking
 * This component helps verify that mobile detection and deep linking work correctly
 */
export function MobileTwitterTest() {
  const [appInfo, setAppInfo] = useState<ReturnType<typeof getMobileAppInfo> | null>(null);
  const [testResult, setTestResult] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Get mobile app info on component mount
    const info = getMobileAppInfo();
    setAppInfo(info);
  }, []);

  const handleTestTwitter = async () => {
    setIsLoading(true);
    setTestResult(null);
    
    try {
      const testText = "Testing mobile Twitter deep linking! 🚀 #TearsOfTheLeft";
      const success = await openXWithFallback(testText);
      
      if (success) {
        setTestResult('✅ Twitter opened successfully!');
      } else {
        setTestResult('❌ Failed to open Twitter');
      }
    } catch (error) {
      setTestResult(`❌ Error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  if (!appInfo) {
    return (
      <Card className="border-accent/30 bg-secondary/5">
        <CardContent className="p-4">
          <div className="flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-accent"></div>
            <span className="text-sm">Loading device info...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const urls = createXUrls("Test message");

  return (
    <Card className="border-accent/30 bg-secondary/5">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg">
          {appInfo.isMobile ? (
            <Smartphone className="h-5 w-5 text-accent" />
          ) : (
            <Monitor className="h-5 w-5 text-accent" />
          )}
          Mobile Twitter Deep Link Test
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Device Information */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Device Information:</h4>
          
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground">Mobile:</span>
              {appInfo.isMobile ? (
                <Badge variant="default" className="text-xs">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Yes
                </Badge>
              ) : (
                <Badge variant="secondary" className="text-xs">
                  <XCircle className="h-3 w-3 mr-1" />
                  No
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground">Platform:</span>
              <Badge variant="outline" className="text-xs">
                {appInfo.platform}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground">iOS:</span>
              {isIOS() ? (
                <Badge variant="default" className="text-xs">Yes</Badge>
              ) : (
                <Badge variant="secondary" className="text-xs">No</Badge>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground">Android:</span>
              {isAndroid() ? (
                <Badge variant="default" className="text-xs">Yes</Badge>
              ) : (
                <Badge variant="secondary" className="text-xs">No</Badge>
              )}
            </div>
          </div>
        </div>

        {/* Twitter URLs */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Generated Twitter URLs:</h4>
          
          <div className="space-y-2 text-xs">
            {appInfo.platform === 'ios' && (
              <div className="p-2 bg-accent/5 rounded border">
                <div className="font-medium text-accent mb-1">iOS App Deep Link:</div>
                <div className="font-mono text-muted-foreground break-all">
                  {urls.iosApp}
                </div>
              </div>
            )}
            
            {appInfo.platform === 'android' && (
              <div className="p-2 bg-accent/5 rounded border">
                <div className="font-medium text-accent mb-1">Android Intent:</div>
                <div className="font-mono text-muted-foreground break-all">
                  {urls.androidIntent.substring(0, 100)}...
                </div>
              </div>
            )}
            
            <div className="p-2 bg-accent/5 rounded border">
              <div className="font-medium text-accent mb-1">Web Fallback:</div>
              <div className="font-mono text-muted-foreground break-all">
                {urls.web}
              </div>
            </div>
          </div>
        </div>

        {/* Test Button */}
        <div className="space-y-3">
          <Button
            onClick={handleTestTwitter}
            disabled={isLoading}
            className="w-full"
            variant="outline"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                Testing...
              </>
            ) : (
              <>
                <Twitter className="h-4 w-4 mr-2" />
                Test Twitter Opening
              </>
            )}
          </Button>
          
          {testResult && (
            <div className="p-2 bg-accent/5 rounded border text-sm">
              {testResult}
            </div>
          )}
        </div>

        {/* Instructions */}
        <div className="text-xs text-muted-foreground p-3 bg-accent/5 rounded border">
          <div className="flex items-start gap-2">
            <Info className="h-3 w-3 mt-0.5 text-accent" />
            <div>
              <p className="font-medium mb-1">How to test:</p>
              <ul className="list-disc list-inside space-y-1">
                <li>Click &ldquo;Test Twitter Opening&rdquo; button</li>
                <li>On mobile: Should try to open Twitter app first</li>
                <li>If app not installed: Falls back to mobile web</li>
                <li>On desktop: Opens Twitter web in new tab</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
